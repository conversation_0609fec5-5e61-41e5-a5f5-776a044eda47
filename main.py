"""
Trask AI Platform - Main Application Entry Point

A clean, modular Streamlit application for showcasing AI solutions
and data science capabilities.
"""
import streamlit as st

# Import application modules
from src.config.styles import CSS_STYLES
from src.utils.navigation import initialize_session_state, get_current_page
from src.components.header import render_title_with_back_button
from src.pages.home import render_home_page
from src.pages.category import render_category_page


def main():
    """Main application entry point."""
    # Configure Streamlit page
    st.set_page_config(
        page_title="Trask",
        page_icon="🧠",
        layout="wide",
        initial_sidebar_state="collapsed"
    )

    # Initialize navigation state
    initialize_session_state()

    # Apply custom CSS styles
    st.markdown(CSS_STYLES, unsafe_allow_html=True)

    # Render header with navigation
    render_title_with_back_button()

    # Route to appropriate page
    current_page = get_current_page()

    if current_page == 'home':
        render_home_page()
    elif current_page == 'category':
        render_category_page()
    else:
        st.error(f"Unknown page: {current_page}")


if __name__ == "__main__":
    main()