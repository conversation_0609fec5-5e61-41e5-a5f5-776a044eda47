# Trask AI Platform

A clean, modular Streamlit application for showcasing AI solutions and data science capabilities.

## 🏗️ Project Structure

```
├── main.py                 # Main application entry point
├── images/                 # SVG icons and assets
│   ├── brain.svg
│   ├── calculator.svg
│   ├── chart.svg
│   ├── database.svg
│   ├── server-crash.svg
│   └── snowflake.svg
└── src/                    # Source code modules
    ├── __init__.py
    ├── config/             # Configuration and styling
    │   └── styles.py       # CSS styles and color palette
    ├── components/         # Reusable UI components
    │   ├── cards.py        # Category and agent card components
    │   └── header.py       # Header and navigation components
    ├── data/               # Data management
    │   ├── agents.py       # Agent data for all categories
    │   └── categories.py   # Category definitions
    ├── pages/              # Page components
    │   ├── category.py     # Category page with agents
    │   └── home.py         # Home page with category grid
    └── utils/              # Utility functions
        ├── image_utils.py  # Image handling and base64 encoding
        └── navigation.py   # Navigation state management
```

## 🎨 Design System

### Color Palette
- **Background**: `#002234`
- **Highlights**: `#00DD66`
- **Text**: `white`
- **Windows/Subsections**: `#004161`
- **Company Brand**: `#0055FF`
- **Demo Links**: `#49DE80`

### Typography
- **Main Title**: "trask" (larger, brand color) + "AIPlatform"
- **Subtitles**: Large, bold, white text
- **Body Text**: Left-aligned, white

## 🚀 Features

### Navigation
- **Home Page**: Category selection grid
- **Category Pages**: Agent listings with search
- **Back Navigation**: Return to home from any page

### Interactive Elements
- **Clickable Cards**: Navigate between pages
- **Search Functionality**: Filter agents by name/description
- **Hover Effects**: Visual feedback on interactive elements

### Responsive Design
- **Grid Layout**: 2 rows × 3 columns for categories
- **Card System**: Consistent styling across components
- **Mobile Friendly**: Responsive column layouts

## 🔧 Technical Features

### Modular Architecture
- **Separation of Concerns**: Logic, UI, and data separated
- **Reusable Components**: DRY principle applied
- **Clean Imports**: Organized module structure

### Image Handling
- **Base64 Encoding**: Proper SVG rendering in Streamlit
- **Error Handling**: Graceful fallbacks for missing images
- **Optimized Loading**: Efficient image management

### State Management
- **Session State**: Persistent navigation state
- **Clean Routing**: Simple page routing system
- **Data Flow**: Predictable state updates

## 📊 Data Structure

### Categories
Each category includes:
- `name`: Display name
- `description`: Category description
- `image`: Associated icon key

### Agents
Each agent includes:
- `name`: Agent name
- `description`: Short description
- `demo_url`: Link to demo (future implementation)

## 🎯 Usage

### Running the Application
```bash
streamlit run main.py
```

### Adding New Categories
1. Add category to `src/data/categories.py`
2. Add corresponding image to `images/` folder
3. Update image mapping in `src/utils/image_utils.py`

### Adding New Agents
1. Add agents to appropriate category in `src/data/agents.py`
2. Agents automatically appear in category pages

## 🔮 Future Enhancements

- **Demo Pages**: Individual agent demonstration pages
- **Authentication**: User login and personalization
- **Analytics**: Usage tracking and insights
- **API Integration**: Real-time data connections
- **Mobile App**: React Native companion app

## 🏢 Company Information

**Trask** is a Data Science and Integration company specializing in:
- **Snowflake**: Cloud data warehouse solutions
- **Databricks**: Unified analytics platform
- **AWS**: Cloud infrastructure and storage

---

*Built with ❤️ using Streamlit and modern Python practices*
