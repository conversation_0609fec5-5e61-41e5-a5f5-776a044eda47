"""
Agent data for different categories
"""

# AI Agents data
AI_AGENTS = [
    {
        "name": "Customer Service Bot",
        "description": "Automated customer support with natural language processing and sentiment analysis",
        "demo_url": "/demo/customer-service-bot"
    },
    {
        "name": "Data Analysis Assistant", 
        "description": "Intelligent data processing and insights generation with automated reporting",
        "demo_url": "/demo/data-analysis-assistant"
    },
    {
        "name": "Content Generator",
        "description": "AI-powered content creation and optimization for marketing and communications",
        "demo_url": "/demo/content-generator"
    },
    {
        "name": "Document Processor",
        "description": "Automated document analysis, extraction, and classification system",
        "demo_url": "/demo/document-processor"
    },
    {
        "name": "Predictive Analytics Engine",
        "description": "Machine learning models for forecasting and trend analysis",
        "demo_url": "/demo/predictive-analytics"
    },
    {
        "name": "Chatbot Assistant",
        "description": "Conversational AI for internal operations and customer engagement",
        "demo_url": "/demo/chatbot-assistant"
    }
]

# Data Analytics agents
DATA_ANALYTICS_AGENTS = [
    {
        "name": "Snowflake Data Processor",
        "description": "Advanced data processing and transformation for Snowflake warehouses",
        "demo_url": "/demo/snowflake-processor"
    },
    {
        "name": "Business Intelligence Dashboard",
        "description": "Real-time analytics and reporting with interactive visualizations",
        "demo_url": "/demo/bi-dashboard"
    },
    {
        "name": "ETL Pipeline Manager",
        "description": "Automated extract, transform, and load operations for data integration",
        "demo_url": "/demo/etl-manager"
    }
]

# Financial Modelling agents
FINANCIAL_AGENTS = [
    {
        "name": "Risk Assessment Model",
        "description": "Comprehensive financial risk analysis and portfolio optimization",
        "demo_url": "/demo/risk-assessment"
    },
    {
        "name": "Revenue Forecasting Engine",
        "description": "Predictive modeling for revenue and growth projections",
        "demo_url": "/demo/revenue-forecasting"
    },
    {
        "name": "Investment Analyzer",
        "description": "Automated investment analysis and recommendation system",
        "demo_url": "/demo/investment-analyzer"
    }
]


def get_agents_by_category(category_id: str) -> list:
    """
    Get agents for a specific category.
    
    Args:
        category_id (str): Category identifier
        
    Returns:
        list: List of agents for the category
    """
    agent_mapping = {
        'ai-agents': AI_AGENTS,
        'data-analytics': DATA_ANALYTICS_AGENTS,
        'financial-modelling': FINANCIAL_AGENTS,
        'snowflake-integration': [],  # To be implemented
        'databricks-platform': [],   # To be implemented
        'aws-services': []            # To be implemented
    }
    
    return agent_mapping.get(category_id, [])


def search_agents(agents: list, search_term: str) -> list:
    """
    Filter agents based on search term.
    
    Args:
        agents (list): List of agent dictionaries
        search_term (str): Search term to filter by
        
    Returns:
        list: Filtered list of agents
    """
    if not search_term:
        return agents
    
    search_lower = search_term.lower()
    filtered_agents = []
    
    for agent in agents:
        if (search_lower in agent['name'].lower() or 
            search_lower in agent['description'].lower()):
            filtered_agents.append(agent)
    
    return filtered_agents
