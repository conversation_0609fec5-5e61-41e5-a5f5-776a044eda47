"""
Category data and configuration
"""

# Category definitions
CATEGORIES = {
    'ai-agents': {
        'name': 'AI Agents',
        'description': 'Intelligent automation and decision-making systems',
        'image': 'brain'
    },
    'data-analytics': {
        'name': 'Data Analytics', 
        'description': 'Advanced data processing and insights generation',
        'image': 'chart'
    },
    'financial-modelling': {
        'name': 'Financial Modelling',
        'description': 'Sophisticated financial analysis and forecasting',
        'image': 'calculator'
    },
    'snowflake-integration': {
        'name': 'Snowflake Integration',
        'description': 'Cloud data warehouse solutions and analytics',
        'image': 'snowflake'
    },
    'databricks-platform': {
        'name': 'Databricks Platform',
        'description': 'Unified analytics platform for big data and machine learning',
        'image': 'database'
    },
    'aws-services': {
        'name': 'AWS Services',
        'description': 'Cloud infrastructure and data storage solutions',
        'image': 'server'
    }
}


def get_category(category_id: str) -> dict:
    """
    Get category data by ID.
    
    Args:
        category_id (str): Category identifier
        
    Returns:
        dict: Category data or None if not found
    """
    return CATEGORIES.get(category_id)


def get_all_categories() -> dict:
    """
    Get all categories.
    
    Returns:
        dict: All category data
    """
    return CATEGORIES


def get_category_list() -> list:
    """
    Get list of all category IDs.
    
    Returns:
        list: List of category IDs
    """
    return list(CATEGORIES.keys())
