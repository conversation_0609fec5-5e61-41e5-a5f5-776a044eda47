"""
Navigation utilities for managing page state and routing
"""
import streamlit as st


def initialize_session_state():
    """Initialize session state variables for navigation."""
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'home'
    if 'selected_category' not in st.session_state:
        st.session_state.selected_category = None


def navigate_to_category(category_id: str, category_name: str, category_description: str):
    """
    Navigate to a category page.
    
    Args:
        category_id (str): Unique identifier for the category
        category_name (str): Display name of the category
        category_description (str): Description of the category
    """
    st.session_state.current_page = 'category'
    st.session_state.selected_category = {
        'id': category_id,
        'name': category_name,
        'description': category_description
    }


def navigate_to_home():
    """Navigate back to the home page."""
    st.session_state.current_page = 'home'
    st.session_state.selected_category = None


def get_current_page() -> str:
    """
    Get the current page from session state.
    
    Returns:
        str: Current page identifier
    """
    return st.session_state.get('current_page', 'home')


def get_selected_category() -> dict:
    """
    Get the currently selected category from session state.
    
    Returns:
        dict: Selected category data or None
    """
    return st.session_state.get('selected_category', None)
