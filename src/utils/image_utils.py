"""
Image utility functions for handling SVG files and base64 encoding
"""
import base64
import os


def get_base64_image(image_path: str) -> str:
    """
    Convert an image file to base64 encoding for use in HTML.
    
    Args:
        image_path (str): Path to the image file
        
    Returns:
        str: Base64 encoded string or None if file not found
    """
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    except FileNotFoundError:
        print(f"Warning: Image file not found: {image_path}")
        return None
    except Exception as e:
        print(f"Error encoding image {image_path}: {e}")
        return None


def create_image_src(base64_data: str, image_type: str = "svg+xml") -> str:
    """
    Create a data URL from base64 encoded image data.
    
    Args:
        base64_data (str): Base64 encoded image data
        image_type (str): MIME type of the image (default: svg+xml)
        
    Returns:
        str: Data URL string or empty string if no data
    """
    if base64_data:
        return f"data:image/{image_type};base64,{base64_data}"
    return ""


def get_all_category_images() -> dict:
    """
    Load all category images and return as base64 encoded dictionary.
    
    Returns:
        dict: Dictionary with image names as keys and base64 data as values
    """
    image_files = {
        'brain': 'images/brain.svg',
        'chart': 'images/chart.svg', 
        'calculator': 'images/calculator.svg',
        'snowflake': 'images/snowflake.svg',
        'database': 'images/database.svg',
        'server': 'images/server-crash.svg'
    }
    
    images = {}
    for name, path in image_files.items():
        images[name] = get_base64_image(path)
    
    return images
