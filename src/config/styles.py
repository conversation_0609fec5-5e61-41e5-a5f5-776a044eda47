"""
CSS Styles and Configuration for Trask AI Platform
"""

# Color palette
COLORS = {
    'background': '#0F172B',
    'highlights': '#00DD66',
    'text': 'white',
    'windows': '#0F172B',
    'brand': '#0055FF',
    'demo_link': '#49DE80'
}

# CSS Styles
CSS_STYLES = """
<style>
    /* Main app background */
    .stApp {
        background-color: #0F172B;
        color: white;
    }

    /* Sidebar styling */
    .css-1d391kg {
        background-color: #0F172B;
    }

    /* Main content area */
    .main .block-container {
        background-color: #0F172B;
        color: white;
    }
    
    /* Custom title styling */
    .custom-title {
        color: #00DD66;
        font-weight: bold;
        text-align: left;
        margin-bottom: 2rem;
        margin-left: 2rem;
        margin-top: 1rem;
        font-family: 'Arial', sans-serif;
    }
    
    .title-trask {
        color: #0055FF;
        font-size: 1.5em;
    }

    .title-ai {
        color: #00DD66;
        font-size: 1.5em;
    }


    /* Subtitle styling */
    .subtitle {
        font-size: 1.875rem;
        font-weight: bold;
        color: white;
        margin-bottom: 2rem;
        margin-left: 2rem;
        font-family: 'Arial', sans-serif;
    }
    
    /* Menu card icon and content styling */
    
    .menu-card-icon {
        text-align: left;
        margin-bottom: 1rem;
    }
    
    .menu-card-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: white !important;
        margin-bottom: 1rem;
        text-align: left;
    }
    
    .menu-card-description {
        color: #D1D5DB !important;
        font-size: 1rem;
        line-height: 1.5;
        text-align: left;
    }
    
    .menu-icon-img {
        width: 48px;
        height: 48px;
        filter: brightness(0) saturate(100%) invert(64%) sepia(95%) saturate(459%) hue-rotate(92deg) brightness(108%) contrast(101%);
        display: block;
        margin: 0;
    }
    
    /* Override Streamlit's default text colors */
    .stMarkdown, .stText {
        color: white !important;
    }
    
    /* Highlights color */
    .highlight {
        color: #00DD66;
    }
    
    /* Windows/Subsections */
    .stContainer, .stColumns {
        background-color: #0F172B;
        border-radius: 10px;
        padding: 1rem;
    }
    
    /* Agent card styling */
    
    .agent-card-title {
        font-size: 1.25rem;
        font-weight: bold;
        color: white !important;
        margin-bottom: 0.5rem;
    }
    
    .agent-card-description {
        color: #D1D5DB !important;
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 1rem;
    }
    


    /* Back button */
    .stButton > button, button[kind="primary"], button[kind="secondary"] {
        color: #49DE80 !important;
        background-color: #1E293B !important;
        border: 2px solid transparent !important;
        border-radius: 12px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        font-size: 1rem !important;
        flex: 1;
        margin-bottom: 1rem;
        margin-top: 1rem;
    }

    /* Buttons */
    .stButton > button:hover, button[kind="primary"]:hover, button[kind="secondary"]:hover {
        background-color: #1E293B !important;
        border-color: #00DD66 !important;
        transform: translateY(-5px) !important;
        box-shadow: 0 10px 25px rgba(0, 221, 102, 0.2) !important;
    }

    /* Card container for positioning */
    .card-container {
        position: relative;
        margin-bottom: 1rem;
    }

    /* Category card content - visual styling */
    .category-card-content {
        background-color: #1E293B;
        border-radius: 12px;
        padding: 2rem;
        min-width: 300px;
        flex: 1;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        position: relative;
        z-index: 1;
    }
</style>
"""
