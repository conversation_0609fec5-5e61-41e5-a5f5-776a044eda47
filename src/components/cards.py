"""
Card components for displaying categories and agents
"""
import streamlit as st
from src.utils.image_utils import create_image_src
from src.utils.navigation import navigate_to_category


def render_category_card(category_id: str, category_data: dict, image_src: str, col):
    """
    Render a category card with navigation functionality.

    Args:
        category_id (str): Category identifier
        category_data (dict): Category information
        image_src (str): Base64 encoded image source
        col: Streamlit column object
    """ 
    with col:
        st.markdown(f"""
        <div class="category-card-content">
            <div class="menu-card-icon">
                <img src="{image_src}" class="menu-icon-img" alt="{category_data['name']} Icon">
            </div>
            <div class="menu-card-title">
                {category_data['name']}
            </div>
            <div class="menu-card-description">
                {category_data['description']}
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("View Demo",
                    key=f"{category_id}_btn",
                    help=f"Click to explore {category_data['name']}",
                    use_container_width=True):
            navigate_to_category(category_id, category_data['name'], category_data['description'])
            st.rerun()

def render_agent_card(agent: dict):
    """
    Render an agent card with demo link.

    Args:
        agent (dict): Agent information containing name, description, and demo_url
    """
    st.markdown('<div class="card-container">', unsafe_allow_html=True)

    st.markdown(f"""
    <div class="category-card-content">
        <div class="agent-card-title">{agent['name']}</div>
        <div class="agent-card-description">{agent['description']}</div>
    </div>
    """, unsafe_allow_html=True)

    if st.button("View Demo",
                key=f"agent_{agent['name'].replace(' ', '_').lower()}_btn",
                help=f"View demo for {agent['name']}",
                use_container_width=True):
        # TODO: Implement agent demo navigation
        st.info(f"Demo for {agent['name']} - Coming Soon!")

    st.markdown('</div>', unsafe_allow_html=True)


def render_category_grid(categories: dict, images: dict):
    """
    Render the complete category grid with two rows.
    
    Args:
        categories (dict): All category data
        images (dict): Base64 encoded images
    """
    # First row
    col1, col2, col3 = st.columns(3)
    
    # Map categories to columns for first row
    first_row_categories = [
        ('ai-agents', col1),
        ('data-analytics', col2), 
        ('financial-modelling', col3)
    ]
    
    for category_id, col in first_row_categories:
        if category_id in categories:
            category_data = categories[category_id]
            image_key = category_data['image']
            image_src = create_image_src(images.get(image_key, ''))
            render_category_card(category_id, category_data, image_src, col)
    
    # Second row
    col4, col5, col6 = st.columns(3)
    
    # Map categories to columns for second row
    second_row_categories = [
        ('snowflake-integration', col4),
        ('databricks-platform', col5),
        ('aws-services', col6)
    ]
    
    for category_id, col in second_row_categories:
        if category_id in categories:
            category_data = categories[category_id]
            image_key = category_data['image']
            image_src = create_image_src(images.get(image_key, ''))
            render_category_card(category_id, category_data, image_src, col)
