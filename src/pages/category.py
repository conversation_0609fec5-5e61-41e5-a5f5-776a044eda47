"""
Category page component
"""
import streamlit as st
from src.components.cards import render_agent_card
from src.data.agents import get_agents_by_category, search_agents
from src.utils.navigation import get_selected_category


def render_category_page():
    """Render the category page with agents and search functionality."""
    category = get_selected_category()
    
    if not category:
        st.error("No category selected. Please go back to home.")
        return
    
    # Display category title and description
    st.markdown(f"""
    <div class="subtitle">
        {category['name']}
    </div>
    <div style="color: white; font-size: 1.1rem; margin: 0 2rem 2rem 2rem;">
        {category['description']}
    </div>
    """, unsafe_allow_html=True)
    
    # Search bar
    search_term = st.text_input(
        label="search_term",
        label_visibility="hidden",
        placeholder="Search use cases...",
        key="search_agents"
    )
    
    # Get agents for this category
    agents = get_agents_by_category(category['id'])
    
    if agents:
        # Filter agents based on search
        filtered_agents = search_agents(agents, search_term)
        
        # Display section header
        st.markdown(f"""
        <div style="margin: 2rem;">
            <h3 style="color: white; margin-bottom: 1rem;">Available {category['name']}</h3>
        </div>
        """, unsafe_allow_html=True)
        
        # Display filtered agents
        if filtered_agents:
            for agent in filtered_agents:
                render_agent_card(agent)
        else:
            st.markdown("""
            <div style="margin: 2rem; color: white;">
                <p>No agents found matching your search criteria.</p>
            </div>
            """, unsafe_allow_html=True)
    else:
        # No agents available yet
        st.markdown(f"""
        <div style="margin: 2rem; color: white;">
            <h3>Coming Soon</h3>
            <p>Agents for {category['name']} are being developed. Check back soon!</p>
        </div>
        """, unsafe_allow_html=True)
